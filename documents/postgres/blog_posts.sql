-- =====================================================
-- BLOG SYSTEM SCHEMA
-- Clean and optimized blog management system
-- =====================================================

-- Create blog_posts table
CREATE TABLE public.blog_posts (
  id SERIAL PRIMARY KEY,
  title TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  excerpt TEXT NOT NULL,
  content TEXT NOT NULL,
  featured_image TEXT,
  author_key TEXT NOT NULL DEFAULT 'team',
  category_slug TEXT NOT NULL DEFAULT 'ai-technology',
  tags TEXT[] DEFAULT '{}',
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  featured BOOLEAN DEFAULT false,
  meta_title TEXT,
  meta_description TEXT,
  read_time_minutes INTEGER DEFAULT 5,
  view_count INTEGER DEFAULT 0,
  published_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes
CREATE INDEX idx_blog_posts_slug ON public.blog_posts (slug);
CREATE INDEX idx_blog_posts_published_at ON public.blog_posts (published_at DESC);
CREATE INDEX idx_blog_posts_status ON public.blog_posts (status);
CREATE INDEX idx_blog_posts_featured ON public.blog_posts (featured, published_at DESC);
CREATE INDEX idx_blog_posts_category_slug ON public.blog_posts (category_slug);
CREATE INDEX idx_blog_posts_tags ON public.blog_posts USING gin(tags);
CREATE INDEX idx_blog_posts_search ON public.blog_posts USING gin(to_tsvector('english', title || ' ' || excerpt || ' ' || content));

-- Create trigger for auto-updating updated_at
CREATE TRIGGER trg_update_blog_posts_timestamp 
  BEFORE UPDATE ON public.blog_posts 
  FOR EACH ROW 
  EXECUTE FUNCTION public.update_modified_column();

-- =====================================================
-- BLOG QUERY FUNCTIONS
-- =====================================================

-- Get published blog posts with pagination, filtering, and search
CREATE FUNCTION public.get_blog_posts(
  param_limit INTEGER DEFAULT 10,
  param_offset INTEGER DEFAULT 0,
  param_category_slug TEXT DEFAULT NULL,
  param_search TEXT DEFAULT NULL,
  param_featured_only BOOLEAN DEFAULT FALSE
)
RETURNS TABLE (
  id INTEGER,
  title TEXT,
  slug TEXT,
  excerpt TEXT,
  featured_image TEXT,
  author_key TEXT,
  category_slug TEXT,
  tags TEXT[],
  featured BOOLEAN,
  read_time_minutes INTEGER,
  view_count INTEGER,
  published_at TIMESTAMP WITH TIME ZONE,
  total_count BIGINT
)
LANGUAGE plpgsql STABLE AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id, p.title, p.slug, p.excerpt, p.featured_image, p.author_key, 
    p.category_slug, p.tags, p.featured, p.read_time_minutes, 
    p.view_count, p.published_at, COUNT(*) OVER() as total_count
  FROM public.blog_posts p
  WHERE p.status = 'published'
    AND p.published_at <= NOW()
    AND (param_category_slug IS NULL OR p.category_slug = param_category_slug)
    AND (param_featured_only = FALSE OR p.featured = TRUE)
    AND (param_search IS NULL OR to_tsvector('english', p.title || ' ' || p.excerpt || ' ' || p.content) @@ plainto_tsquery('english', param_search))
  ORDER BY p.featured DESC, p.published_at DESC
  LIMIT param_limit OFFSET param_offset;
END $$;

-- Get single blog post by slug
CREATE FUNCTION public.get_blog_post_by_slug(param_slug TEXT)
RETURNS TABLE (
  id INTEGER, title TEXT, slug TEXT, excerpt TEXT, content TEXT,
  featured_image TEXT, author_key TEXT, category_slug TEXT, tags TEXT[],
  featured BOOLEAN, meta_title TEXT, meta_description TEXT,
  read_time_minutes INTEGER, view_count INTEGER, published_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql VOLATILE AS $$
BEGIN
  UPDATE public.blog_posts 
  SET view_count = public.blog_posts.view_count + 1 
  WHERE public.blog_posts.slug = param_slug;
  
  RETURN QUERY
  SELECT p.id, p.title, p.slug, p.excerpt, p.content, p.featured_image, 
         p.author_key, p.category_slug, p.tags, p.featured, p.meta_title, 
         p.meta_description, p.read_time_minutes, p.view_count, p.published_at
  FROM public.blog_posts p
  WHERE p.slug = param_slug AND p.status = 'published' AND p.published_at <= NOW();
END $$;

-- Get related blog posts by category
CREATE FUNCTION public.get_related_posts(param_post_id INTEGER, param_limit INTEGER DEFAULT 3)
RETURNS TABLE (
  id INTEGER, title TEXT, slug TEXT, excerpt TEXT, featured_image TEXT,
  author_key TEXT, category_slug TEXT, read_time_minutes INTEGER, published_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql STABLE AS $$
BEGIN
  RETURN QUERY
  SELECT p.id, p.title, p.slug, p.excerpt, p.featured_image, p.author_key,
         p.category_slug, p.read_time_minutes, p.published_at
  FROM public.blog_posts p
  WHERE p.status = 'published' AND p.published_at <= NOW() AND p.id != param_post_id
    AND p.category_slug = (SELECT bp.category_slug FROM public.blog_posts bp WHERE bp.id = param_post_id)
  ORDER BY p.published_at DESC LIMIT param_limit;
END $$;

-- Create blog statistics view
CREATE VIEW public.blog_stats AS
SELECT 
  (SELECT COUNT(*) FROM public.blog_posts WHERE status = 'published') as published_posts,
  (SELECT COUNT(*) FROM public.blog_posts WHERE status = 'draft') as draft_posts,
  (SELECT COUNT(DISTINCT category_slug) FROM public.blog_posts WHERE status = 'published') as active_categories,
  (SELECT COUNT(DISTINCT tag) FROM public.blog_posts p, unnest(p.tags) AS tag WHERE p.status = 'published') as active_tags,
  (SELECT SUM(view_count) FROM public.blog_posts) as total_views,
  (SELECT AVG(read_time_minutes) FROM public.blog_posts WHERE status = 'published') as avg_read_time;

-- =====================================================
-- SAMPLE DATA
-- =====================================================

-- Insert sample blog posts
INSERT INTO public.blog_posts (title, slug, excerpt, content, author_key, category_slug, tags, status, featured, read_time_minutes, published_at) VALUES
('How AI is Revolutionizing Customer Service in Southeast Asia', 'ai-revolutionizing-customer-service-southeast-asia', 'Discover how businesses in Cambodia and the region are using AI-powered chatbots to handle customer inquiries 24/7, reducing response times and improving customer satisfaction.', 'The digital transformation in Southeast Asia has accelerated rapidly, with AI-powered customer service leading the charge. Businesses are discovering that AI chatbots can handle customer inquiries around the clock, reducing response times from hours to seconds.', 'team', 'ai-technology', ARRAY['AI', 'Customer Service', 'Southeast Asia', 'Automation'], 'published', true, 5, '2024-12-15 00:00:00'::timestamp with time zone),
('Setting Up Your First Social Media Automation', 'setting-up-first-social-media-automation', 'A step-by-step guide to connecting Facebook, Instagram, and Telegram to your ChhlatBot. Learn best practices for training your AI and optimizing response accuracy.', 'Getting started with social media automation can seem overwhelming, but with the right approach, you can have your ChhlatBot responding to customers across Facebook, Instagram, and Telegram within hours.', 'technical', 'tutorial', ARRAY['Tutorial', 'Setup', 'Social Media', 'Automation'], 'published', false, 8, '2024-12-10 00:00:00'::timestamp with time zone),
('Multi-Language Support: Why Khmer Voice Matters', 'multi-language-support-khmer-voice-matters', 'Explore the importance of native language support in customer service automation. See how ChhlatBot''s Khmer voice capabilities are helping local businesses connect better with customers.', 'The importance of native language support in customer service cannot be overstated, especially in diverse markets like Cambodia. ChhlatBot''s Khmer voice capabilities represent a significant breakthrough in localized customer service automation.', 'product', 'features', ARRAY['Khmer', 'Multi-language', 'Voice', 'Localization'], 'published', false, 6, '2024-12-05 00:00:00'::timestamp with time zone),
('Small Business Success Stories with ChhlatBot', 'small-business-success-stories-chhlatbot', 'Real case studies from Cambodian SMEs who transformed their customer service with automation. Learn how restaurants, retail stores, and service providers are scaling their operations.', 'Real case studies from Cambodian SMEs show the transformative power of AI automation in small business operations. From busy restaurants managing hundreds of daily inquiries to retail stores handling product questions.', 'marketing', 'case-studies', ARRAY['Success Stories', 'SME', 'Cambodia'], 'published', false, 7, '2024-12-01 00:00:00'::timestamp with time zone),
('The Future of Social Media Customer Service', 'future-social-media-customer-service', 'Industry trends and predictions for AI-powered customer service. What''s coming next in social media automation and how businesses can prepare for the future.', 'Industry trends and predictions show exciting developments in AI-powered customer service over the next few years. As social media platforms continue to evolve and customer expectations rise.', 'strategy', 'industry-insights', ARRAY['Future', 'Trends', 'Industry', 'Predictions'], 'published', false, 10, '2024-11-25 00:00:00'::timestamp with time zone);