/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        'deep-blue': '#0a0b1e',
        'jade-purple': '#6135e6',
        'jade-purple-dark': '#4c28b8',
        'jade-purple-light': '#7c4dff',
      },
      fontFamily: {
        'title': ['var(--font-geist-sans)', 'sans-serif'],
      },
    },
  },
  plugins: [],
}