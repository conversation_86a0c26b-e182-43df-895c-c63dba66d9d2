'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { MagnifyingGlassIcon, TagIcon, CalendarDaysIcon, ClockIcon, ArrowRightIcon, UserIcon } from '@heroicons/react/24/outline'
import Footer from '@/components/Footer'

interface BlogPost {
  id: number
  title: string
  slug: string
  excerpt: string
  featured_image?: string
  author: string
  category: {
    name: string
    slug: string
    color: string
  }
  tags: string[]
  featured: boolean
  read_time: string
  view_count: number
  published_at: string
}

interface BlogCategory {
  name: string
  slug: string
  color: string
  postCount: number
}

interface PaginationInfo {
  currentPage: number
  totalPages: number
  totalCount: number
  hasNextPage: boolean
  hasPreviousPage: boolean
}

export default function BlogPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [currentPage, setCurrentPage] = useState(1)
  const [posts, setPosts] = useState<BlogPost[]>([])
  const [categories, setCategories] = useState<BlogCategory[]>([])
  const [pagination, setPagination] = useState<PaginationInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [featuredPost, setFeaturedPost] = useState<BlogPost | null>(null)

  // Progressive loading states
  const [categoriesLoaded, setCategoriesLoaded] = useState(false)
  const [featuredLoaded, setFeaturedLoaded] = useState(false)
  const initialPostsLoaded = useRef(false)
  const categoriesLoading = useRef(false)
  const featuredLoading = useRef(false)
  const postsLoading = useRef(false)
  
  const postsPerPage = 6

  // Fetch categories
  const fetchCategories = useCallback(async () => {
    if (categories.length === 0 && !categoriesLoaded && !categoriesLoading.current) {
      categoriesLoading.current = true
      try {
        const categoriesRes = await fetch('/api/blog/categories')
        const categoriesData = await categoriesRes.json()
        if (categoriesData.success) {
          setCategories(categoriesData.data)
          setCategoriesLoaded(true)
        } else {
          console.error('❌ Categories fetch failed:', categoriesData.error)
        }
      } catch (error) {
        console.error('💥 Error fetching categories:', error)
      } finally {
        categoriesLoading.current = false
      }
    }
  }, [])

  // Fetch featured post
  const fetchFeaturedPost = useCallback(async () => {
    if (!featuredPost && !featuredLoaded && !featuredLoading.current) {
      featuredLoading.current = true
      try {
        const featuredRes = await fetch('/api/blog/posts?featured=true&limit=1')
        const featuredData = await featuredRes.json()
        if (featuredData.success && featuredData.data.posts.length > 0) {
          setFeaturedPost(featuredData.data.posts[0])
          setFeaturedLoaded(true)
        } else {
          console.log('ℹ️ No featured posts found')
          setFeaturedLoaded(true)
        }
      } catch (error) {
        console.error('💥 Error fetching featured post:', error)
        setFeaturedLoaded(true)
      } finally {
        featuredLoading.current = false
      }
    }
  }, [])

  // Fetch regular posts
  const fetchPosts = useCallback(async () => {
    if (postsLoading.current) return
    postsLoading.current = true
    try {
      setLoading(true)

      // Build query parameters
      const params = new URLSearchParams({
        limit: postsPerPage.toString(),
        page: currentPage.toString(),
      })

      if (selectedCategory !== 'All' && categories.length > 0) {
        const categorySlug = categories.find(cat => cat.name === selectedCategory)?.slug
        if (categorySlug) {
          params.set('category', categorySlug)
        }
      }

      if (searchQuery.trim()) {
        params.set('search', searchQuery.trim())
      }

      // Fetch regular posts (excluding featured)
      const postsUrl = `/api/blog/posts?${params}`
      const postsRes = await fetch(postsUrl)
      const postsData = await postsRes.json()

      if (postsData.success) {
        // Filter out featured post from regular posts
        const regularPosts = postsData.data.posts.filter((post: BlogPost) => !post.featured)
        setPosts(regularPosts)
        setPagination(postsData.data.pagination)
        console.log('✅ Posts loaded')
      } else {
        console.error('❌ Posts fetch failed:', postsData.error)
      }
    } catch (error) {
      console.error('💥 Error fetching blog data:', error)
    } finally {
      setLoading(false)
      postsLoading.current = false
    }
  }, [])

  // Progressive loading: Load categories and featured post immediately
  useEffect(() => {
    fetchCategories()
    fetchFeaturedPost()
  }, [])

  // Load posts when categories are loaded (initial load only)
  useEffect(() => {
    if (categoriesLoaded && !initialPostsLoaded.current) {
      console.log('🔄 Loading initial posts...')
      initialPostsLoaded.current = true
      fetchPosts()
    }
  }, [categoriesLoaded])

  // Handle filter changes (page, category selection) - only after initial load
  useEffect(() => {
    if (categoriesLoaded && initialPostsLoaded.current) {
      fetchPosts()
    }
  }, [currentPage, selectedCategory])

  // Handle search with debounce
  useEffect(() => {
    if (!categoriesLoaded || !initialPostsLoaded.current) return

    const timeoutId = setTimeout(() => {
      if (currentPage !== 1) {
        setCurrentPage(1) // Reset to first page when searching
      } else {
        fetchPosts()
      }
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [searchQuery])

  const allCategories = ['All', ...categories.map(cat => cat.name)]

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="min-h-screen bg-deep-blue flex flex-col">
      {/* Background Effects */}
      <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white/3 rounded-full blur-[150px] -z-10"></div>
      <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-jade-purple/3 rounded-full blur-[120px] -z-10"></div>

      {/* Navigation Header */}
      <nav className="border-b border-white/10 backdrop-blur-sm bg-deep-blue/80 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <a href="https://chhlatbot.com" className="flex items-center space-x-2">
              <Image
                src="/images/white_tran_logo.svg"
                alt="ChhlatBot"
                width={150}
                height={40}
                className="h-8 w-auto"
              />
            </a>
            <div className="flex items-center space-x-6">
              <Link href="/docs" className="text-gray-300 hover:text-white transition-colors">Docs</Link>
              <Link href="/blog" className="text-gray-300 hover:text-white transition-colors font-semibold text-jade-purple">Blog</Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="flex-grow">
        <div className="container mx-auto px-4 py-12">
          {/* Hero Section */}
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-5xl md:text-6xl font-bold mb-6 text-white font-title">
              ChhlatBot <span className="text-jade-purple">Blog</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Discover insights on AI automation, customer service innovation, and the future of business communication
            </p>
          </motion.div>

          {/* Featured Post */}
          {!featuredLoaded ? (
            <motion.div
              className="mb-16 relative overflow-hidden rounded-2xl border border-white/20 bg-gradient-to-br from-jade-purple/10 to-transparent"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="grid lg:grid-cols-2 gap-8 p-8 lg:p-12">
                <div className="flex flex-col justify-center">
                  <div className="h-6 bg-white/10 rounded mb-6 animate-pulse"></div>
                  <div className="h-8 bg-white/10 rounded mb-4 animate-pulse"></div>
                  <div className="h-8 bg-white/10 rounded mb-6 animate-pulse w-3/4"></div>
                  <div className="h-4 bg-white/10 rounded mb-2 animate-pulse"></div>
                  <div className="h-4 bg-white/10 rounded mb-8 animate-pulse w-5/6"></div>
                </div>
                <div className="aspect-video bg-white/10 rounded-lg animate-pulse"></div>
              </div>
            </motion.div>
          ) : featuredPost ? (
            <motion.article
              className="mb-16 relative overflow-hidden rounded-2xl border border-white/20 bg-gradient-to-br from-jade-purple/10 to-transparent"
              style={{
                boxShadow: '0 0 20px rgba(97, 53, 230, 0.15), inset 0 0 30px rgba(255, 255, 255, 0.05)'
              }}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="grid lg:grid-cols-2 gap-8 p-8 lg:p-12">
                <div className="flex flex-col justify-center">
                  <div className="flex items-center gap-3 mb-6">
                    <span className="bg-jade-purple text-white px-4 py-2 rounded-full text-sm font-semibold flex items-center gap-2">
                      <TagIcon className="w-4 h-4" />
                      Featured
                    </span>
                    <span className="font-medium" style={{ color: featuredPost.category.color }}>{featuredPost.category.name}</span>
                  </div>
                  <h2 className="text-3xl lg:text-4xl font-bold mb-6 text-white leading-tight">
                    {featuredPost.title}
                  </h2>
                  <p className="text-gray-300 mb-8 text-lg leading-relaxed">
                    {featuredPost.excerpt}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-6 text-sm text-gray-400">
                      <div className="flex items-center gap-2">
                        <UserIcon className="w-4 h-4" />
                        <span>{featuredPost.author}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CalendarDaysIcon className="w-4 h-4" />
                        <span>{formatDate(featuredPost.published_at)}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <ClockIcon className="w-4 h-4" />
                        <span>{featuredPost.read_time}</span>
                      </div>
                    </div>
                  </div>
                  <Link
                    href={`/blog/${featuredPost.slug}`}
                    className="mt-8 inline-flex items-center gap-2 bg-jade-purple text-white px-6 py-3 rounded-lg hover:bg-jade-purple/90 transition-all duration-300 font-medium group w-fit"
                  >
                    Read Full Article
                    <ArrowRightIcon className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </div>
                <div className="relative">
                  <div className="aspect-video rounded-xl bg-gradient-to-br from-jade-purple/20 to-white/5 border border-white/10 flex items-center justify-center">
                    <div className="text-gray-400 text-center">
                      <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-jade-purple/20 flex items-center justify-center">
                        <TagIcon className="w-8 h-8 text-jade-purple" />
                      </div>
                      <p className="font-medium">Featured Article</p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.article>
          ) : null}

          {/* Search and Filter Section */}
          <motion.div
            className="mb-12 p-6 rounded-xl border border-white/10 bg-white/5 backdrop-blur-sm"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              {/* Search Bar */}
              <div className="relative flex-1 max-w-md">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search articles..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-jade-purple focus:ring-2 focus:ring-jade-purple/20 transition-all"
                  disabled={!categoriesLoaded}
                />
              </div>

              {/* Category Filter */}
              <div className="flex flex-wrap gap-2">
                {!categoriesLoaded ? (
                  // Loading skeleton for categories
                  <>
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="h-10 w-20 bg-white/10 rounded-lg animate-pulse"></div>
                    ))}
                  </>
                ) : (
                  allCategories.map((category) => (
                  <button
                    key={category}
                    onClick={() => {
                      setSelectedCategory(category)
                      setCurrentPage(1)
                    }}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                      selectedCategory === category
                        ? 'bg-jade-purple text-white'
                        : 'bg-white/10 text-gray-300 hover:bg-white/20 hover:text-white'
                    }`}
                    disabled={loading}
                  >
                    {category}
                    {category !== 'All' && (() => {
                      const categoryData = categories.find(cat => cat.name === category)
                      return categoryData && categoryData.postCount > 0 && (
                        <span className="ml-1 text-xs opacity-75">
                          ({categoryData.postCount})
                        </span>
                      )
                    })()}
                  </button>
                  ))
                )}
              </div>
            </div>
          </motion.div>

          {/* Results Summary */}
          <div className="mb-8 text-gray-300">
            <p>
              {loading ? (
                'Loading articles...'
              ) : (
                <>
                  {searchQuery || selectedCategory !== 'All'
                    ? `Found ${pagination?.totalCount || 0} article${(pagination?.totalCount || 0) !== 1 ? 's' : ''}`
                    : `Showing ${pagination?.totalCount || 0} article${(pagination?.totalCount || 0) !== 1 ? 's' : ''}`
                  }
                  {selectedCategory !== 'All' && ` in ${selectedCategory}`}
                  {searchQuery && ` matching "${searchQuery}"`}
                </>
              )}
            </p>
          </div>

          {/* Blog Posts Grid */}
          {loading ? (
            <div className="grid md:grid-cols-2 xl:grid-cols-3 gap-8 mb-12">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="animate-pulse">
                  <div className="aspect-video bg-white/10 rounded-xl mb-4"></div>
                  <div className="space-y-3">
                    <div className="h-4 bg-white/10 rounded w-3/4"></div>
                    <div className="h-6 bg-white/10 rounded"></div>
                    <div className="h-4 bg-white/10 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : posts.length > 0 ? (
            <div className="grid md:grid-cols-2 xl:grid-cols-3 gap-8 mb-12">
              {posts.map((post, index) => (
                <motion.article
                  key={post.id}
                  className="group relative overflow-hidden rounded-xl border border-white/20 bg-white/5 backdrop-blur-sm hover:border-white/30 transition-all duration-300"
                  style={{
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1), inset 0 0 20px rgba(255, 255, 255, 0.05)'
                  }}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 * index }}
                >
                  {/* Image Placeholder */}
                  <div className="aspect-video bg-gradient-to-br from-jade-purple/20 to-white/5 border-b border-white/10 flex items-center justify-center">
                    <div className="text-gray-400 text-center">
                      <TagIcon className="w-8 h-8 mx-auto mb-2" />
                      <p className="text-sm">{post.category.name}</p>
                    </div>
                  </div>

                  <div className="p-6">
                    {/* Category and Tags */}
                    <div className="flex items-center gap-2 mb-4">
                      <span 
                        className="text-sm font-medium px-2 py-1 rounded"
                        style={{ 
                          backgroundColor: `${post.category.color}20`,
                          color: post.category.color 
                        }}
                      >
                        {post.category.name}
                      </span>
                      {post.tags.slice(0, 2).map((tag) => (
                        <span key={tag} className="text-gray-400 text-xs bg-white/10 px-2 py-1 rounded">
                          {tag}
                        </span>
                      ))}
                    </div>

                    {/* Title */}
                    <h3 className="text-xl font-bold mb-3 text-white line-clamp-2">
                      <Link href={`/blog/${post.slug}`}>
                        {post.title}
                      </Link>
                    </h3>

                    {/* Excerpt */}
                    <p className="text-gray-300 mb-4 line-clamp-3 leading-relaxed">
                      {post.excerpt}
                    </p>

                    {/* Meta Information */}
                    <div className="flex items-center justify-between pt-4 border-t border-white/10">
                      <div className="flex items-center gap-4 text-xs text-gray-400">
                        <div className="flex items-center gap-1">
                          <UserIcon className="w-3 h-3" />
                          <span>{post.author}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <CalendarDaysIcon className="w-3 h-3" />
                          <span>{formatDate(post.published_at)}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <ClockIcon className="w-3 h-3" />
                          <span>{post.read_time}</span>
                        </div>
                      </div>
                    </div>

                    {/* Read More Link */}
                    <Link
                      href={`/blog/${post.slug}`}
                      className="inline-flex items-center gap-2 text-jade-purple font-medium mt-4"
                    >
                      Read More
                      <ArrowRightIcon className="w-4 h-4" />
                    </Link>
                  </div>
                </motion.article>
              ))}
            </div>
          ) : (
            <motion.div
              className="text-center py-16 text-gray-400"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <MagnifyingGlassIcon className="w-16 h-16 mx-auto mb-4 text-gray-500" />
              <h3 className="text-xl font-semibold mb-2 text-gray-300">No articles found</h3>
              <p>Try adjusting your search or filter criteria.</p>
            </motion.div>
          )}

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <motion.div
              className="flex justify-center items-center gap-2 mb-12"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={!pagination.hasPreviousPage || loading}
                className="px-4 py-2 rounded-lg bg-white/10 text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-white/20 transition-colors"
              >
                Previous
              </button>
              
              <div className="flex gap-2">
                {Array.from({ length: Math.min(pagination.totalPages, 5) }, (_, i) => {
                  const page = i + Math.max(1, currentPage - 2)
                  if (page > pagination.totalPages) return null
                  return (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      disabled={loading}
                      className={`w-10 h-10 rounded-lg font-medium transition-colors ${
                        currentPage === page
                          ? 'bg-jade-purple text-white'
                          : 'bg-white/10 text-gray-300 hover:bg-white/20 hover:text-white disabled:opacity-50'
                      }`}
                    >
                      {page}
                    </button>
                  )
                })}
              </div>
              
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, pagination.totalPages))}
                disabled={!pagination.hasNextPage || loading}
                className="px-4 py-2 rounded-lg bg-white/10 text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-white/20 transition-colors"
              >
                Next
              </button>
            </motion.div>
          )}

        </div>
      </main>

      <Footer />
    </div>
  )
}