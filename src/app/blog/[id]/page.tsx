'use client'

import { useState, useEffect } from 'react'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { ArrowLeftIcon, CalendarDaysIcon, ClockIcon, UserIcon, TagIcon, ShareIcon } from '@heroicons/react/24/outline'
import Footer from '@/components/Footer'

interface BlogPostDetail {
  id: number
  title: string
  slug: string
  excerpt: string
  content: string
  featured_image?: string
  author: {
    name: string
    email?: string
  }
  category: {
    name: string
    slug: string
    color: string
  }
  tags: string[]
  featured: boolean
  meta: {
    title: string
    description: string
  }
  read_time: string
  view_count: number
  published_at: string
}

interface RelatedPost {
  id: number
  title: string
  slug: string
  excerpt: string
  featured_image?: string
  author: string
  category: {
    name: string
    slug: string
    color: string
  }
  read_time: string
  published_at: string
}

interface BlogPostPageProps {
  params: { id: string }
}

interface BlogPostResponse {
  post: BlogPostDetail
  relatedPosts: RelatedPost[]
}

export default function BlogPostPage({ params }: BlogPostPageProps) {
  const [blogData, setBlogData] = useState<BlogPostResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { id: slug } = params

  useEffect(() => {
    const fetchBlogPost = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/blog/posts/${slug}`)
        const data = await response.json()
        
        if (data.success) {
          setBlogData(data.data)
        } else {
          setError(data.error || 'Blog post not found')
        }
      } catch (err) {
        console.error('Error fetching blog post:', err)
        setError('Failed to load blog post')
      } finally {
        setLoading(false)
      }
    }

    fetchBlogPost()
  }, [slug])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-deep-blue flex items-center justify-center">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-jade-purple mx-auto mb-4"></div>
          <p>Loading article...</p>
        </div>
      </div>
    )
  }

  if (error || !blogData) {
    notFound()
  }

  const { post, relatedPosts } = blogData

  return (
    <div className="min-h-screen bg-deep-blue">
      {/* Background Effects */}
      <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white/3 rounded-full blur-[150px] -z-10"></div>
      <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-jade-purple/3 rounded-full blur-[120px] -z-10"></div>

      {/* Navigation Header */}
      <nav className="border-b border-white/10 backdrop-blur-sm bg-deep-blue/80 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-2">
              <Image
                src="/images/white_tran_logo.svg"
                alt="ChhlatBot"
                width={150}
                height={40}
                className="h-8 w-auto"
              />
            </Link>
            <div className="flex items-center space-x-6">
              <Link href="/blog" className="text-gray-300 hover:text-white transition-colors">Blog</Link>
              <Link href="/docs" className="text-gray-300 hover:text-white transition-colors">Docs</Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Back to Blog Link */}
      <div className="container mx-auto px-4 py-6">
        <Link 
          href="/blog"
          className="inline-flex items-center gap-2 text-gray-300 hover:text-white transition-colors"
        >
          <ArrowLeftIcon className="w-4 h-4" />
          Back to Blog
        </Link>
      </div>

      {/* Article Content */}
      <article className="container mx-auto px-4 pb-16">
        <div className="max-w-4xl mx-auto">
          {/* Article Header */}
          <motion.header
            className="mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {/* Featured Badge and Category */}
            <div className="flex items-center gap-3 mb-6">
              {post.featured && (
                <span className="bg-jade-purple text-white px-4 py-2 rounded-full text-sm font-semibold flex items-center gap-2">
                  <TagIcon className="w-4 h-4" />
                  Featured
                </span>
              )}
              <span 
                className="font-medium px-3 py-1 rounded-full"
                style={{ 
                  backgroundColor: `${post.category.color}20`,
                  color: post.category.color 
                }}
              >
                {post.category.name}
              </span>
            </div>

            {/* Title */}
            <h1 className="text-4xl lg:text-5xl font-bold mb-8 text-white leading-tight font-title">
              {post.title}
            </h1>

            {/* Meta Information */}
            <div className="flex flex-wrap items-center gap-6 text-gray-400 mb-8">
              <div className="flex items-center gap-2">
                <UserIcon className="w-5 h-5" />
                <span>{typeof post.author === 'string' ? post.author : post.author.name}</span>
              </div>
              <div className="flex items-center gap-2">
                <CalendarDaysIcon className="w-5 h-5" />
                <span>{formatDate(post.published_at)}</span>
              </div>
              <div className="flex items-center gap-2">
                <ClockIcon className="w-5 h-5" />
                <span>{post.read_time}</span>
              </div>
              <div className="flex items-center gap-2">
                <span>{post.view_count} views</span>
              </div>
              <button className="flex items-center gap-2 hover:text-white transition-colors">
                <ShareIcon className="w-5 h-5" />
                <span>Share</span>
              </button>
            </div>

            {/* Tags */}
            <div className="flex flex-wrap gap-2">
              {post.tags.map((tag) => (
                <span 
                  key={tag}
                  className="text-gray-300 text-sm bg-white/10 px-3 py-1 rounded-full border border-white/20"
                >
                  {tag}
                </span>
              ))}
            </div>
          </motion.header>

          {/* Featured Image */}
          <motion.div
            className="mb-12 aspect-video rounded-2xl bg-gradient-to-br from-jade-purple/20 to-white/5 border border-white/10 flex items-center justify-center overflow-hidden relative"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {post.featured_image ? (
              <Image
                src={post.featured_image}
                alt={post.title}
                fill
                className="object-cover"
              />
            ) : (
              <div className="text-center text-gray-400">
                <TagIcon className="w-16 h-16 mx-auto mb-4" />
                <p className="font-medium">Article Image</p>
                <p className="text-sm">{post.category.name}</p>
              </div>
            )}
          </motion.div>

          {/* Article Content */}
          <motion.div
            className="blog-content max-w-none"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <div 
              dangerouslySetInnerHTML={{ 
                __html: post.content
                  .split('\n')
                  .map(line => {
                    if (line.startsWith('# ')) {
                      return `<h1>${line.slice(2)}</h1>`
                    } else if (line.startsWith('## ')) {
                      return `<h2>${line.slice(3)}</h2>`
                    } else if (line.startsWith('### ')) {
                      return `<h3>${line.slice(4)}</h3>`
                    } else if (line.startsWith('- ')) {
                      return `<li>${line.slice(2)}</li>`
                    } else if (line.trim() === '') {
                      return ''
                    } else if (line.includes('**') && line.includes(':**')) {
                      return `<p><strong>${line.replace(/\*\*(.*?)\*\*:/g, '$1:')}</strong></p>`
                    } else if (line.includes('**')) {
                      return `<p>${line.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')}</p>`
                    } else if (line.startsWith('*') && line.endsWith('*')) {
                      return `<p style="color: #6135e6; font-style: italic; text-align: center; margin: 2rem 0; font-size: 1.125rem;">${line.slice(1, -1).replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2">$1</a>')}</p>`
                    } else {
                      return `<p>${line.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2">$1</a>')}</p>`
                    }
                  })
                  .join('')
              }}
            />
          </motion.div>

          {/* Related Posts */}
          {relatedPosts.length > 0 && (
            <motion.section
              className="mt-16 pt-12 border-t border-white/10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <h2 className="text-2xl font-bold text-white mb-8">Related Articles</h2>
              <div className="grid md:grid-cols-3 gap-8">
                {relatedPosts.map((relatedPost) => (
                  <article 
                    key={relatedPost.id}
                    className="group border border-white/20 rounded-xl p-6 bg-white/5 backdrop-blur-sm hover:border-white/30 transition-all duration-300"
                  >
                    <div className="aspect-video bg-gradient-to-br from-jade-purple/20 to-white/5 rounded-lg mb-4 flex items-center justify-center overflow-hidden relative">
                      {relatedPost.featured_image ? (
                        <Image
                          src={relatedPost.featured_image}
                          alt={relatedPost.title}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <TagIcon className="w-8 h-8 text-gray-400" />
                      )}
                    </div>
                    <span 
                      className="text-sm font-medium"
                      style={{ color: relatedPost.category.color }}
                    >
                      {relatedPost.category.name}
                    </span>
                    <h3 className="font-bold text-white mt-2 mb-3 line-clamp-2">
                      <Link href={`/blog/${relatedPost.slug}`} className="hover:text-jade-purple transition-colors">
                        {relatedPost.title}
                      </Link>
                    </h3>
                    <p className="text-gray-300 text-sm line-clamp-3 mb-4">
                      {relatedPost.excerpt}
                    </p>
                    <div className="flex items-center gap-3 text-xs text-gray-400">
                      <span>{formatDate(relatedPost.published_at)}</span>
                      <span>•</span>
                      <span>{relatedPost.read_time}</span>
                    </div>
                  </article>
                ))}
              </div>
            </motion.section>
          )}

          {/* Call to Action */}
          <motion.section
            className="mt-16 p-8 rounded-2xl border border-jade-purple/20 bg-gradient-to-r from-jade-purple/10 to-transparent text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <h3 className="text-2xl font-bold text-white mb-4">
              Ready to Transform Your Customer Service?
            </h3>
            <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
              Join thousands of businesses already using ChhlatBot to automate their customer service and boost satisfaction.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/"
                className="bg-jade-purple text-white px-8 py-3 rounded-lg hover:bg-jade-purple/90 transition-colors font-medium"
              >
                Get Started Free
              </Link>
              <Link
                href="/blog"
                className="border border-jade-purple text-jade-purple px-8 py-3 rounded-lg hover:bg-jade-purple/10 transition-colors font-medium"
              >
                Read More Articles
              </Link>
            </div>
          </motion.section>
        </div>
      </article>

      <Footer />
    </div>
  )
}