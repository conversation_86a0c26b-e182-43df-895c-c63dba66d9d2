import { NextResponse } from 'next/server'
import { getBlogCategories } from '@/lib/blog'

export async function GET() {
  try {
    const categories = await getBlogCategories()

    return NextResponse.json({
      success: true,
      data: categories
    })
  } catch (error) {
    console.error('💥 API Error fetching blog categories:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch categories', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}