import { NextRequest, NextResponse } from 'next/server'
import { getBlogPosts } from '@/lib/blog'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = request.nextUrl
    const limit = parseInt(searchParams.get('limit') || '10')
    const page = parseInt(searchParams.get('page') || '1')
    const category = searchParams.get('category') || null
    const search = searchParams.get('search') || null
    const featured = searchParams.get('featured') === 'true'

    const result = await getBlogPosts({
      limit,
      page,
      category,
      search,
      featured
    })

    return NextResponse.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('💥 API Error fetching blog posts:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch blog posts', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}