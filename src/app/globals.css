@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 10, 11, 30;
  --background-end-rgb: 10, 11, 30;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

/* Blog content styling */
.blog-content {
  @apply text-gray-300 leading-relaxed;
}

.blog-content h1 {
  @apply text-3xl font-bold text-white mb-6 mt-8;
}

.blog-content h2 {
  @apply text-2xl font-semibold text-white mb-4 mt-6;
}

.blog-content h3 {
  @apply text-xl font-medium text-white mb-3 mt-5;
}

.blog-content p {
  @apply mb-4 text-gray-300 leading-relaxed;
}

.blog-content li {
  @apply mb-2 text-gray-300 ml-6;
}

.blog-content a {
  @apply text-jade-purple hover:text-jade-purple-light underline;
}

.blog-content strong {
  @apply text-white font-semibold;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}