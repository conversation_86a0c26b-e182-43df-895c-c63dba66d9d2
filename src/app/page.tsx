'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import Footer from '@/components/Footer'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-deep-blue">
      {/* Background Effects */}
      <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white/3 rounded-full blur-[150px] -z-10"></div>
      <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-jade-purple/3 rounded-full blur-[120px] -z-10"></div>

      {/* Navigation */}
      <nav className="border-b border-white/10 backdrop-blur-sm bg-deep-blue/80 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-2">
              <div className="text-2xl font-bold text-jade-purple">
                ChhlatBot
              </div>
            </Link>
            <div className="flex items-center space-x-6">
              <Link href="/docs" className="text-gray-300 hover:text-white transition-colors">Documentation</Link>
              <Link href="/blog" className="text-gray-300 hover:text-white transition-colors">Blog</Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <main className="container mx-auto px-4 py-20">
        <motion.div
          className="text-center max-w-4xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-5xl md:text-7xl font-bold mb-8 text-white">
            AI-Powered <span className="text-jade-purple">Customer Service</span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-12 leading-relaxed max-w-3xl mx-auto">
            Transform your customer service with intelligent automation for Facebook Messenger, Instagram DM, and Telegram. 
            Handle inquiries 24/7 with personalized responses.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
            <Link
              href="/docs"
              className="bg-jade-purple text-white px-8 py-4 rounded-lg hover:bg-jade-purple/90 transition-all duration-300 font-semibold text-lg"
            >
              Get Started
            </Link>
            <Link
              href="/blog"
              className="border border-jade-purple text-jade-purple px-8 py-4 rounded-lg hover:bg-jade-purple/10 transition-all duration-300 font-semibold text-lg"
            >
              Read Blog
            </Link>
          </div>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          className="grid md:grid-cols-3 gap-8 mt-20"
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {[
            {
              title: "Multi-Platform",
              description: "Connect Facebook Messenger, Instagram DM, and Telegram in one dashboard",
              icon: "🔗"
            },
            {
              title: "AI-Powered",
              description: "Advanced natural language processing for intelligent customer interactions",
              icon: "🧠"
            },
            {
              title: "24/7 Automation",
              description: "Handle customer inquiries around the clock with instant responses",
              icon: "⚡"
            }
          ].map((feature, index) => (
            <motion.div
              key={index}
              className="bg-white/5 border border-white/10 rounded-xl p-8 hover:bg-white/10 hover:border-jade-purple/30 transition-all duration-300"
              whileHover={{ scale: 1.05 }}
            >
              <div className="text-4xl mb-4">{feature.icon}</div>
              <h3 className="text-xl font-semibold text-white mb-4">{feature.title}</h3>
              <p className="text-gray-300">{feature.description}</p>
            </motion.div>
          ))}
        </motion.div>

        {/* CTA Section */}
        <motion.div
          className="mt-20 text-center bg-gradient-to-br from-jade-purple/10 to-jade-purple/5 border border-jade-purple/20 rounded-2xl p-12"
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <h2 className="text-3xl font-bold text-white mb-6">
            Ready to Transform Your Customer Service?
          </h2>
          <p className="text-gray-300 mb-8 text-lg max-w-2xl mx-auto">
            Join thousands of businesses already using ChhlatBot to automate their customer service and boost satisfaction.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/docs"
              className="bg-jade-purple text-white px-8 py-3 rounded-lg hover:bg-jade-purple/90 transition-colors font-medium"
            >
              Read Documentation
            </Link>
            <Link
              href="/blog"
              className="border border-jade-purple text-jade-purple px-8 py-3 rounded-lg hover:bg-jade-purple/10 transition-colors font-medium"
            >
              Explore Blog
            </Link>
          </div>
        </motion.div>
      </main>

      <Footer />
    </div>
  )
}