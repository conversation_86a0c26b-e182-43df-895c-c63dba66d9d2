'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'

export default function Footer() {
  const currentYear = new Date().getFullYear()

  const footerLinks = [
    {
      title: 'Product',
      links: [
        { name: 'Features', href: 'https://chhlatbot.com#features' },
        { name: 'Pricing', href: 'https://chhlatbot.com#pricing' },
        { name: 'How it Works', href: 'https://chhlatbot.com#how-it-works' },
        { name: 'FAQ', href: 'https://chhlatbot.com#faq' },
      ]
    },
    {
      title: 'Company',
      links: [
        { name: 'Blog', href: '/blog' },
        { name: 'Documentation', href: '/docs' },
      ]
    },
  ]

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <footer className="text-white relative overflow-hidden mt-16">
      {/* Modern clean footer */}
      <div className="container mx-auto px-4 py-12 relative z-10">
        <div className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-8 border border-white/20 hover:border-white/30 transition-all duration-300 overflow-hidden">
          {/* Simple background overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-jade-purple/3 to-transparent rounded-2xl"></div>

          {/* Content */}
          <div className="relative z-10">
            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
              <div className="col-span-2">
                <a
                  href="https://chhlatbot.com"
                  className="mb-6 inline-block group"
                >
                  <Image
                    src="/images/white_tran_logo.svg"
                    alt="ChhlatBot"
                    width={150}
                    height={40}
                    className="h-8 w-auto"
                  />
                </a>
                <p className="font-body mb-8 text-zinc-300 text-sm sm:text-base leading-relaxed">
                  AI-powered customer service automation for social media platforms. 
                  Handle customer inquiries 24/7 with intelligent responses.
                </p>
              </div>

              {footerLinks.map((section, index) => (
                <div key={index}>
                  <h4 className="text-lg font-semibold mb-6 font-title text-white">{section.title}</h4>
                  <ul className="space-y-4">
                    {section.links.map((link, linkIndex) => (
                      <li key={linkIndex}>
                        <a
                          href={link.href}
                          className="text-zinc-400 hover:text-white transition-colors duration-300 font-body text-sm"
                        >
                          {link.name}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>

            {/* Bottom section with improved styling */}
            <div className="mt-12 pt-8 border-t border-white/10">
              <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <p className="text-zinc-400 text-sm font-body">
                  &copy; {currentYear} <span className="text-jade-purple font-semibold">ChhlatBot</span>. All rights reserved.
                </p>
                <div className="flex flex-wrap justify-center gap-6 text-sm font-body">
                  <a
                    href="https://chhlatbot.com/privacy"
                    className="text-zinc-400 hover:text-white transition-colors duration-300 hover:underline underline-offset-4"
                  >
                    Privacy Policy
                  </a>
                  <a
                    href="https://chhlatbot.com/terms"
                    className="text-zinc-400 hover:text-white transition-colors duration-300 hover:underline underline-offset-4"
                  >
                    Terms of Service
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}