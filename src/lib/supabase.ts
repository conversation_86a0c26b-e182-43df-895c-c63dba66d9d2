import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_KEY!

export const supabase = createClient(supabaseUrl, supabaseKey)

// Query utilities similar to postgres.ts but for Supabase
export async function queryOne<T = any>(
  table: string,
  query: any = {}
): Promise<T | null> {
  try {
    const { data, error } = await supabase
      .from(table)
      .select('*')
      .match(query)
      .single()

    if (error) throw error
    return data
  } catch (error) {
    console.error(`Supabase queryOne error (${table}):`, error)
    return null
  }
}

export async function queryMany<T = any>(
  table: string,
  query: any = {},
  options: {
    limit?: number
    offset?: number
    orderBy?: { column: string; ascending?: boolean }
  } = {}
): Promise<T[]> {
  try {
    let queryBuilder = supabase
      .from(table)
      .select('*')
      .match(query)

    if (options.limit) {
      queryBuilder = queryBuilder.limit(options.limit)
    }

    if (options.offset) {
      queryBuilder = queryBuilder.range(options.offset, options.offset + (options.limit || 10) - 1)
    }

    if (options.orderBy) {
      queryBuilder = queryBuilder.order(options.orderBy.column, { 
        ascending: options.orderBy.ascending ?? false 
      })
    }

    const { data, error } = await queryBuilder

    if (error) throw error
    return data || []
  } catch (error) {
    console.error(`Supabase queryMany error (${table}):`, error)
    return []
  }
}

export async function executeQuery<T = any>(query: string, params: any[] = []): Promise<T[]> {
  try {
    const { data, error } = await supabase.rpc(query.toLowerCase(), 
      params.reduce((acc, val, idx) => {
        acc[`param_${idx}`] = val
        return acc
      }, {} as Record<string, any>)
    )

    if (error) throw error
    return data || []
  } catch (error) {
    console.error('Supabase executeQuery error:', error)
    return []
  }
}