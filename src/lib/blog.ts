import { supabase } from '@/lib/supabase'
import { 
  BLOG_CATEGORIES, 
  BLOG_AUTHORS, 
  getCategoryBySlug, 
  getAuthor,
  type BlogCategory as StaticBlogCategory 
} from '@/lib/blog-constants'

export interface BlogPost {
  id: number
  title: string
  slug: string
  excerpt: string
  featured_image?: string
  author: string
  category: {
    name: string
    slug: string
    color: string
  }
  tags: string[]
  featured: boolean
  read_time: string
  view_count: number
  published_at: string
}

export interface BlogPostDetail {
  id: number
  title: string
  slug: string
  excerpt: string
  content: string
  featured_image?: string
  author: {
    name: string
    email?: string
  }
  category: {
    name: string
    slug: string
    color: string
  }
  tags: string[]
  featured: boolean
  meta: {
    title: string
    description: string
  }
  read_time: string
  view_count: number
  published_at: string
}

// BlogCategory is now imported from static constants
export type BlogCategory = StaticBlogCategory & {
  postCount: number
}

export interface PaginationInfo {
  currentPage: number
  totalPages: number
  totalCount: number
  hasNextPage: boolean
  hasPreviousPage: boolean
}

// Fetch blog posts with filtering and pagination
export async function getBlogPosts({
  limit = 10,
  page = 1,
  category = null,
  search = null,
  featured = false
}: {
  limit?: number
  page?: number
  category?: string | null
  search?: string | null
  featured?: boolean
} = {}) {
  try {
    
    const offset = (page - 1) * limit
    
    let query = supabase
      .from('blog_posts')
      .select('*', { count: 'exact' })
      .eq('status', 'published')
      .lte('published_at', new Date().toISOString())

    // Apply filters
    if (category) {
      query = query.eq('category_slug', category)
    }
    
    if (featured) {
      query = query.eq('featured', true)
    }

    if (search) {
      query = query.or(`title.ilike.%${search}%,excerpt.ilike.%${search}%,content.ilike.%${search}%`)
    }

    // Add pagination and ordering
    query = query
      .order('featured', { ascending: false })
      .order('published_at', { ascending: false })
      .range(offset, offset + limit - 1)


    const { data: posts, error, count } = await query



    if (error) throw error

    const totalCount = count || 0
    const totalPages = Math.ceil(totalCount / limit)

    return {
      posts: (posts || []).map(post => {
        const category = getCategoryBySlug(post.category_slug)
        const author = getAuthor(post.author_key)
        
        return {
          id: post.id,
          title: post.title,
          slug: post.slug,
          excerpt: post.excerpt,
          featured_image: post.featured_image || undefined,
          author: author.name,
          category: {
            name: category?.name || 'Unknown',
            slug: post.category_slug,
            color: category?.color || '#6135e6'
          },
          tags: post.tags || [],
          featured: post.featured,
          read_time: `${post.read_time_minutes} min read`,
          view_count: post.view_count,
          published_at: post.published_at
        }
      }),
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      }
    }
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    throw error
  }
}

// Fetch single blog post by slug
export async function getBlogPostBySlug(slug: string) {
  try {
    // Get the main post
    const { data: post, error: postError } = await supabase
      .from('blog_posts')
      .select('*')
      .eq('slug', slug)
      .eq('status', 'published')
      .lte('published_at', new Date().toISOString())
      .single()

    if (postError || !post) {
      return null
    }

    // Increment view count
    await supabase
      .from('blog_posts')
      .update({ view_count: post.view_count + 1 })
      .eq('id', post.id)

    // Get related posts (same category, different post)
    const { data: relatedPosts, error: relatedError } = await supabase
      .from('blog_posts')
      .select('id, title, slug, excerpt, featured_image, author_key, category_slug, read_time_minutes, published_at')
      .eq('status', 'published')
      .eq('category_slug', post.category_slug)
      .neq('id', post.id)
      .lte('published_at', new Date().toISOString())
      .order('published_at', { ascending: false })
      .limit(3)

    const category = getCategoryBySlug(post.category_slug)
    const author = getAuthor(post.author_key)

    return {
      post: {
        id: post.id,
        title: post.title,
        slug: post.slug,
        excerpt: post.excerpt,
        content: post.content,
        featured_image: post.featured_image || undefined,
        author: {
          name: author.name,
          email: author.email
        },
        category: {
          name: category?.name || 'Unknown',
          slug: post.category_slug,
          color: category?.color || '#6135e6'
        },
        tags: post.tags || [],
        featured: post.featured,
        meta: {
          title: post.meta_title || post.title,
          description: post.meta_description || post.excerpt
        },
        read_time: `${post.read_time_minutes} min read`,
        view_count: post.view_count + 1, // Include the increment
        published_at: post.published_at
      },
      relatedPosts: (relatedPosts || []).map(related => {
        const relatedCategory = getCategoryBySlug(related.category_slug)
        const relatedAuthor = getAuthor(related.author_key)
        
        return {
          id: related.id,
          title: related.title,
          slug: related.slug,
          excerpt: related.excerpt,
          featured_image: related.featured_image || undefined,
          author: relatedAuthor.name,
          category: {
            name: relatedCategory?.name || 'Unknown',
            slug: related.category_slug,
            color: relatedCategory?.color || '#6135e6'
          },
          tags: [], // Tags not needed for related posts
          featured: false,
          read_time: `${related.read_time_minutes} min read`,
          view_count: 0, // Don't show view count for related posts
          published_at: related.published_at
        }
      })
    }
  } catch (error) {
    console.error('Error fetching blog post by slug:', error)
    throw error
  }
}

// Fetch blog categories with post counts (uses static categories + database counts)
export async function getBlogCategories(): Promise<BlogCategory[]> {
  try {
    
    const { data: categoryCounts, error } = await supabase
      .from('blog_posts')
      .select('category_slug')
      .eq('status', 'published')


    if (error) throw error

    // Count posts per category
    const countMap = new Map<string, number>()
    categoryCounts?.forEach(post => {
      const count = countMap.get(post.category_slug) || 0
      countMap.set(post.category_slug, count + 1)
    })
    
    const result = Object.values(BLOG_CATEGORIES).map(cat => ({
      ...cat,
      postCount: countMap.get(cat.slug) || 0
    }))

    return result
  } catch (error) {
    console.error('💥 Error fetching blog categories:', error)
    throw error
  }
}