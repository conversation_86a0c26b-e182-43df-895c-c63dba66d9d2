{"name": "chhlat-learn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start -p 3000", "lint": "next lint"}, "dependencies": {"@supabase/supabase-js": "^2.49.4", "@heroicons/react": "^2.2.0", "framer-motion": "^10.16.4", "next": "14.2.30", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@types/node": "^20.11.0", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.15", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2"}}