# ChhlatBot Learn - Docs & Blog

A dedicated Next.js application for ChhlatBot's documentation and blog system.

## Features

- **Documentation System**: Complete guide for using ChhlatBot
- **Blog System**: Dynamic blog with categories, search, and pagination  
- **Supabase Integration**: Database-driven content management
- **Responsive Design**: Mobile-first approach with modern UI
- **SEO Optimized**: Meta tags, structured data, and performance optimized

## Tech Stack

- **Framework**: Next.js 14.2.30 with App Router
- **Database**: Supabase (PostgreSQL)
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Icons**: Heroicons
- **Language**: TypeScript

## Getting Started

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Set up environment variables**:
   ```bash
   cp .env.example .env.local
   ```
   
   Update `.env.local` with your Supabase credentials:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

3. **Set up Supabase database**:
   - Create tables using the schema from `../docs/postgres/blog_posts.sql`
   - Ensure your Supabase project has the required blog_posts table

4. **Run development server**:
   ```bash
   npm run dev
   ```

5. **Open in browser**:
   - Main site: http://localhost:3001
   - Documentation: http://localhost:3001/docs
   - Blog: http://localhost:3001/blog

## Database Schema

The app expects a `blog_posts` table in Supabase with the following structure:

```sql
CREATE TABLE public.blog_posts (
  id SERIAL PRIMARY KEY,
  title TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  excerpt TEXT NOT NULL,
  content TEXT NOT NULL,
  featured_image TEXT,
  author_key TEXT NOT NULL DEFAULT 'team',
  category_slug TEXT NOT NULL DEFAULT 'ai-technology',
  tags TEXT[] DEFAULT '{}',
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  featured BOOLEAN DEFAULT false,
  meta_title TEXT,
  meta_description TEXT,
  read_time_minutes INTEGER DEFAULT 5,
  view_count INTEGER DEFAULT 0,
  published_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

## Project Structure

```
learn/
├── src/
│   ├── app/
│   │   ├── api/blog/          # Blog API endpoints
│   │   ├── blog/              # Blog pages
│   │   ├── docs/              # Documentation page
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Home page
│   ├── components/
│   │   └── Footer.tsx         # Shared footer component
│   └── lib/
│       ├── blog.ts            # Blog business logic
│       ├── blog-constants.ts  # Static categories/authors
│       └── supabase.ts        # Supabase client
├── package.json
├── tailwind.config.js
└── next.config.js
```

## Available Scripts

- `npm run dev` - Start development server on port 3001
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Deployment

1. Build the application:
   ```bash
   npm run build
   ```

2. Deploy to your preferred platform (Vercel, Netlify, etc.)

3. Set environment variables in your deployment platform

## Features Overview

### Blog System
- Dynamic blog posts from Supabase
- Categories and tags
- Search functionality
- Pagination
- Featured posts
- Related posts
- SEO optimization

### Documentation
- Static documentation with dynamic navigation
- Responsive sidebar
- Search functionality
- Mobile-friendly design
- Smooth scrolling navigation

### Performance
- Static site generation where possible
- Image optimization
- Component-based architecture
- Minimal bundle size